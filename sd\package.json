{"name": "html-space-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "node server.js"}, "dependencies": {"@huggingface/hub": "^1.1.1", "@huggingface/inference": "^3.6.1", "@monaco-editor/react": "^4.7.0", "@tailwindcss/vite": "^4.0.15", "@xenova/transformers": "^2.17.2", "body-parser": "^1.20.3", "classnames": "^2.5.1", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "express": "^4.21.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "tailwindcss": "^4.0.15"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/express": "^5.0.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}