import { useState, useEffect } from "react";
import { RiSparkling2Fill, RiRobotFill } from "react-icons/ri";
import { GrSend } from "react-icons/gr";
import { FiChevronDown } from "react-icons/fi";
import classNames from "classnames";
import { toast } from "react-toastify";

import Login from "../login/login";
import { defaultHTML } from "../../utils/consts";
import SuccessSound from "./../../assets/success.mp3";

interface Agent {
  name: string;
  filename: string;
  description: string;
}

function AskAI({
  html,
  setHtml,
  onScrollToBottom,
  isAiWorking,
  setisAiWorking,
}: {
  html: string;
  setHtml: (html: string) => void;
  onScrollToBottom: () => void;
  isAiWorking: boolean;
  setisAiWorking: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState("");
  const [hasAsked, setHasAsked] = useState(false);
  const [previousPrompt, setPreviousPrompt] = useState("");
  const [agentMode, setAgentMode] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [showAgentDropdown, setShowAgentDropdown] = useState(false);
  const audio = new Audio(SuccessSound);
  audio.volume = 0.5;

  // Load available agents on component mount
  useEffect(() => {
    const loadAgents = async () => {
      try {
        const response = await fetch("/api/agents");
        if (response.ok) {
          const agentList = await response.json();
          setAgents(agentList);
        }
      } catch (error) {
        console.error("Failed to load agents:", error);
      }
    };
    loadAgents();
  }, []);

  const callAi = async () => {
    if (isAiWorking || !prompt.trim()) return;
    setisAiWorking(true);

    let contentResponse = "";
    let lastRenderTime = 0;
    try {
      const request = await fetch("/api/ask-ai", {
        method: "POST",
        body: JSON.stringify({
          prompt,
          ...(html === defaultHTML ? {} : { html }),
          ...(previousPrompt ? { previousPrompt } : {}),
          ...(agentMode && selectedAgent ? {
            agentMode: true,
            agentName: selectedAgent.filename
          } : {}),
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (request && request.body) {
        if (!request.ok) {
          try {
            const res = await request.json();
            if (res.openLogin) {
              setOpen(true);
            } else {
              // don't show toast if it's a login error
              toast.error(res.message || `Request failed with status ${request.status}`);
            }
          } catch (jsonError) {
            // If response is not JSON, try to get text
            try {
              const errorText = await request.text();
              toast.error(errorText || `Request failed with status ${request.status}`);
            } catch (textError) {
              toast.error(`Request failed with status ${request.status}`);
            }
          }
          setisAiWorking(false);
          return;
        }
        const reader = request.body.getReader();
        const decoder = new TextDecoder("utf-8");

        const read = async () => {
          const { done, value } = await reader.read();
          if (done) {
            toast.success("AI responded successfully");
            setPrompt("");
            setPreviousPrompt(prompt);
            setisAiWorking(false);
            setHasAsked(true);
            audio.play();

            // Now we have the complete HTML including </html>, so set it to be sure
            const finalDoc = contentResponse.match(
              /<!DOCTYPE html>[\s\S]*<\/html>/
            )?.[0];
            if (finalDoc) {
              setHtml(finalDoc);
            }

            return;
          }

          const chunk = decoder.decode(value, { stream: true });
          contentResponse += chunk;
          const newHtml = contentResponse.match(/<!DOCTYPE html>[\s\S]*/)?.[0];
          if (newHtml) {
            // Force-close the HTML tag so the iframe doesn't render half-finished markup
            let partialDoc = newHtml;
            if (!partialDoc.includes("</html>")) {
              partialDoc += "\n</html>";
            }

            // Throttle the re-renders to avoid flashing/flicker
            const now = Date.now();
            if (now - lastRenderTime > 300) {
              setHtml(partialDoc);
              lastRenderTime = now;
            }

            if (partialDoc.length > 200) {
              onScrollToBottom();
            }
          }
          read();
        };

        read();
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      setisAiWorking(false);
      toast.error(error.message);
      if (error.openLogin) {
        setOpen(true);
      }
    }
  };

  return (
    <div
      className={`bg-gray-950 rounded-xl py-2 lg:py-2.5 pl-3.5 lg:pl-4 pr-2 lg:pr-2.5 absolute lg:sticky bottom-3 left-3 lg:bottom-4 lg:left-4 w-[calc(100%-1.5rem)] lg:w-[calc(100%-2rem)] z-10 group ${
        isAiWorking ? "animate-pulse" : ""
      }`}
    >
      {/* Agent Mode Toggle */}
      {agents.length > 0 && (
        <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-800">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setAgentMode(!agentMode)}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                agentMode
                  ? "bg-blue-600 text-white"
                  : "bg-gray-800 text-gray-400 hover:bg-gray-700"
              }`}
            >
              <RiRobotFill className="text-sm" />
              Agent Mode
            </button>
            {agentMode && (
              <div className="relative">
                <button
                  onClick={() => setShowAgentDropdown(!showAgentDropdown)}
                  className="flex items-center gap-1 px-2 py-1 bg-gray-800 hover:bg-gray-700 rounded text-xs text-gray-300"
                >
                  {selectedAgent ? selectedAgent.name : "Select Agent"}
                  <FiChevronDown className="text-xs" />
                </button>
                {showAgentDropdown && (
                  <div className="absolute bottom-full mb-1 left-0 bg-gray-800 border border-gray-700 rounded shadow-lg min-w-48 max-h-40 overflow-y-auto z-20">
                    {agents.map((agent) => (
                      <button
                        key={agent.filename}
                        onClick={() => {
                          setSelectedAgent(agent);
                          setShowAgentDropdown(false);
                        }}
                        className="w-full text-left px-3 py-2 text-xs text-gray-300 hover:bg-gray-700 border-b border-gray-700 last:border-b-0"
                      >
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-gray-500 text-xs truncate">{agent.description}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          {agentMode && selectedAgent && (
            <div className="text-xs text-blue-400">
              {selectedAgent.name} Active
            </div>
          )}
        </div>
      )}

      <div className="w-full relative flex items-center justify-between">
        <RiSparkling2Fill className={`text-lg lg:text-xl ${
          agentMode && selectedAgent
            ? "text-blue-500 group-focus-within:text-blue-400"
            : "text-gray-500 group-focus-within:text-pink-500"
        }`} />
        <input
          type="text"
          disabled={isAiWorking}
          className="w-full bg-transparent max-lg:text-sm outline-none pl-3 text-white placeholder:text-gray-500 font-code"
          placeholder={
            agentMode && selectedAgent
              ? `Ask ${selectedAgent.name}...`
              : hasAsked
                ? "What do you want to ask AI next?"
                : "Ask AI anything..."
          }
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              callAi();
            }
          }}
        />
        <button
          disabled={isAiWorking}
          className={`relative overflow-hidden cursor-pointer flex-none flex items-center justify-center rounded-full text-sm font-semibold size-8 text-center text-white shadow-sm dark:shadow-highlight/20 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed disabled:hover:bg-gray-300 ${
            agentMode && selectedAgent
              ? "bg-blue-600 hover:bg-blue-500"
              : "bg-pink-500 hover:bg-pink-400"
          }`}
          onClick={callAi}
        >
          <GrSend className="-translate-x-[1px]" />
        </button>
      </div>
      <div
        className={classNames(
          "h-screen w-screen bg-black/20 fixed left-0 top-0 z-10",
          {
            "opacity-0 pointer-events-none": !open,
          }
        )}
        onClick={() => setOpen(false)}
      ></div>
      <div
        className={classNames(
          "absolute top-0 -translate-y-[calc(100%+8px)] right-0 z-10 w-80 bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-75 overflow-hidden",
          {
            "opacity-0 pointer-events-none": !open,
          }
        )}
      >
        <Login html={html}>
          <p className="text-gray-500 text-sm mb-3">
            You reached the limit of free AI usage. Please login to continue.
          </p>
        </Login>
      </div>
    </div>
  );
}

export default AskAI;
